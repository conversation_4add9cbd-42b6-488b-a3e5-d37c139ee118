import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Header from '../../components/Layout/Header';
import Footer from '../../components/Layout/Footer';
import api from '../../services/api';
import {
  DashboardContainer,
  Main,
  MainHeader,
  MainTitle,
  CourseFilter,
  MainContent,
  CourseItem,
  ImgFrame,
  VideoCountBadge,
  CourseInfo,
  CourseName,
  PlaylistMetadata,
  LoadingText,
  ErrorText,
  NoResultsText
} from './Dashboard.styles';

const Dashboard = () => {
  const navigate = useNavigate();
  const [playlists, setPlaylists] = useState([]);
  const [filteredPlaylists, setFilteredPlaylists] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [thumbnailCache, setThumbnailCache] = useState({});

  // Load playlists on component mount
  useEffect(() => {
    loadPlaylists();
  }, []);

  // Update filtered playlists when playlists or search query changes
  useEffect(() => {
    filterPlaylists();
  }, [playlists, searchQuery, selectedFilter]);

  // Function to fetch thumbnail from YouTube API
  const fetchPlaylistThumbnail = async (youtubePlaylistId) => {
    try {
      // Check cache first
      if (thumbnailCache[youtubePlaylistId]) {
        return thumbnailCache[youtubePlaylistId];
      }

      // Fetch from YouTube API using the test endpoint
      const response = await api.get(`/api/test/youtube/playlist/${youtubePlaylistId}`);
      const thumbnailUrl = response.data?.snippet?.thumbnails?.high?.url ||
                          response.data?.snippet?.thumbnails?.medium?.url ||
                          response.data?.snippet?.thumbnails?.default?.url;

      if (thumbnailUrl) {
        // Cache the result
        setThumbnailCache(prev => ({
          ...prev,
          [youtubePlaylistId]: thumbnailUrl
        }));
        return thumbnailUrl;
      }
    } catch (err) {
      console.warn('Failed to fetch thumbnail for playlist:', youtubePlaylistId, err);
    }
    return null;
  };

  const loadPlaylists = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await api.get('/api/my/playlists');
      const playlistsData = response.data;

      // Fetch missing thumbnails
      const playlistsWithThumbnails = await Promise.all(
        playlistsData.map(async (playlist) => {
          if (!playlist.thumbnail_url && playlist.youtube_playlist_id) {
            const fetchedThumbnail = await fetchPlaylistThumbnail(playlist.youtube_playlist_id);
            return {
              ...playlist,
              thumbnail_url: fetchedThumbnail || playlist.thumbnail_url
            };
          }
          return playlist;
        })
      );

      setPlaylists(playlistsWithThumbnails);
    } catch (err) {
      console.error('Error loading playlists:', err);
      setError(err.message || 'Failed to load playlists. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const filterPlaylists = () => {
    let filtered = playlists;

    // Apply search filter
    if (searchQuery.trim()) {
      filtered = filtered.filter(playlist =>
        playlist.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        playlist.description?.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Apply category filter based on playlist titles
    if (selectedFilter !== 'all') {
      filtered = filtered.filter(playlist => {
        const title = playlist.title?.toLowerCase() || '';
        switch (selectedFilter) {
          case 'tecnica':
            return title.includes('técnica') || title.includes('tecnica');
          case 'laboratorio':
            return title.includes('laboratorio');
          case 'dibujo':
            return title.includes('dibujo');
          case 'color':
            return title.includes('color');
          default:
            return true;
        }
      });
    }

    setFilteredPlaylists(filtered);
  };

  // Handle search functionality
  const handleSearch = (query) => {
    setSearchQuery(query);
  };

  return (
    <DashboardContainer>
      <Header
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        handleSearch={handleSearch}
      />
      <Main>
        <MainHeader>
          <MainTitle>Mis Playlists</MainTitle>
          <CourseFilter
            name="playlist-filter"
            id="playlist-filter"
            value={selectedFilter}
            onChange={(e) => setSelectedFilter(e.target.value)}
          >
            <option value="all">Todas las playlists</option>
            <option value="tecnica">Técnicas</option>
            <option value="laboratorio">Laboratorios</option>
            <option value="dibujo">Dibujo</option>
            <option value="color">Color</option>
          </CourseFilter>
        </MainHeader>
        <MainContent>
          {loading && <LoadingText>Cargando playlists...</LoadingText>}
          {error && <ErrorText>Error: {error}</ErrorText>}
          {!loading && !error && filteredPlaylists.length === 0 && (
            <NoResultsText>
              {searchQuery ? 'No se encontraron playlists que coincidan con tu búsqueda' : 'No tienes acceso a ninguna playlist'}
            </NoResultsText>
          )}
          {!loading && !error && filteredPlaylists.map((playlist) => (
            <CourseItem
              key={playlist.playlist_id}
              onClick={() => navigate(`/playlist/${playlist.playlist_id}`)}
            >
              <ImgFrame>
                <img
                  src={
                    playlist.thumbnail_url ||
                    "https://via.placeholder.com/320x180?text=Loading+Thumbnail"
                  }
                  alt={playlist.title}
                  onError={(e) => {
                    // Fallback to placeholder if thumbnail fails to load
                    if (!e.target.src.includes('placeholder')) {
                      e.target.src = "https://via.placeholder.com/320x180?text=Playlist+Thumbnail";
                    }
                  }}
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover',
                    borderRadius: '8px',
                    display: 'block'
                  }}
                />
                {playlist.video_count && (
                  <VideoCountBadge>{playlist.video_count} videos</VideoCountBadge>
                )}
              </ImgFrame>
              <CourseInfo>
                <CourseName>{playlist.title}</CourseName>
                <PlaylistMetadata>
                  {playlist.description && playlist.description.length > 100
                    ? `${playlist.description.substring(0, 100)}...`
                    : playlist.description}
                  {playlist.granted_at && (
                    <div style={{ fontSize: 'var(--font-size-xs)', color: 'var(--color-text-secondary)', marginTop: '0.25rem' }}>
                      Acceso otorgado: {new Date(playlist.granted_at).toLocaleDateString('es-ES')}
                    </div>
                  )}
                </PlaylistMetadata>
              </CourseInfo>
            </CourseItem>
          ))}
        </MainContent>
      </Main>
      <Footer />
    </DashboardContainer>
  );
};

export default Dashboard;
